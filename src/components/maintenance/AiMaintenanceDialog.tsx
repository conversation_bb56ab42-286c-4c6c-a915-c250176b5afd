
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { MaintenanceTask, Provider } from './types';
import { useMaintenanceTasksQueryV2 } from '@/hooks/useMaintenanceTasksQueryV2';
import { supabase } from '@/integrations/supabase/client';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { useAuth } from '@/contexts/AuthContext';

interface AiMaintenanceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTasksCreated: () => void;
  providers: Provider[];
}

const AiMaintenanceDialog: React.FC<AiMaintenanceDialogProps> = ({
  open,
  onOpenChange,
  onTasksCreated,
  providers
}) => {
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedTasks, setGeneratedTasks] = useState<Partial<MaintenanceTask>[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const { addTask } = useMaintenanceTasksQueryV2();
  const { properties } = usePropertiesQueryV2();
  const { authState } = useAuth();
  const userId = authState?.user?.id;

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
    // Clear previous results when input changes
    if (generatedTasks.length > 0) {
      setGeneratedTasks([]);
    }
    if (error) {
      setError(null);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const pastedText = e.clipboardData.getData('text');
    setInputText(pastedText);
  };

  const handleAnalyze = async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    if (!userId) {
      setError('You must be logged in to use the AI assistant');
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedTasks([]);

    try {
      console.log("Sending request to AI function with text:", inputText);

      // Prepare the property and provider data to send to the AI
      const propertyData = properties.map(p => ({
        id: p.id,
        name: p.name
      }));

      const providerData = providers.map(p => ({
        id: p.id,
        name: p.name
      }));

      const { data, error: invokeError } = await supabase.functions.invoke('ai-maintenance-items', {
        body: {
          text: inputText,
          properties: propertyData,
          providers: providerData,
          userId: userId,
          createTasks: true // Use the new task creation functionality
        },
      });

      console.log("Response from function:", data, "Error:", invokeError);

      if (invokeError) {
        console.error("Invoke error:", invokeError);
        throw new Error(`Error calling AI service: ${invokeError.message}`);
      }

      if (!data) {
        throw new Error('No data received from AI service');
      }

      if (data.error) {
        throw new Error(`AI service error: ${data.error}`);
      }

      // Handle the new response format when tasks are created directly
      if (data.success && data.createdTasks) {
        console.log('AI created tasks directly:', data);

        if (data.createdTasks.length > 0) {
          toast.success(data.message || `Successfully created ${data.createdTasks.length} maintenance tasks`);

          // Show any errors that occurred
          if (data.errors && data.errors.length > 0) {
            console.warn('Some tasks failed to create:', data.errors);
            toast.warning(`${data.errors.length} tasks failed to create. Check console for details.`);
          }

          onTasksCreated();
          onOpenChange(false);
          // Reset form
          setInputText('');
          setGeneratedTasks([]);
        } else {
          toast.error('Failed to create any tasks');
          if (data.errors && data.errors.length > 0) {
            setError(data.errors.join('; '));
          }
        }
      } else if (Array.isArray(data) && data.length > 0) {
        // Fallback to old behavior if createTasks was false
        console.log('AI generated tasks:', data);

        // Process the AI response to ensure it matches our MaintenanceTask type
        const processedTasks = data.map(task => {
          // Format the due date if it exists but isn't already in a compatible format
          let dueDate = task.dueDate || 'No due date';

          // Match any provider name to our existing providers if possible
          let providerId = task.providerId;
          if (!providerId && task.providerName) {
            const matchedProvider = providers.find(p =>
              p.name.toLowerCase() === task.providerName.toLowerCase()
            );
            if (matchedProvider) {
              providerId = matchedProvider.id;
            }
          }

          return {
            ...task,
            status: 'new',
            dueDate,
            providerId,
            severity: task.severity || 'medium' // Ensure severity has a default value
          };
        });

        setGeneratedTasks(processedTasks);
      } else {
        setError('No maintenance tasks identified in the text. Try a more detailed description.');
      }
    } catch (err: any) {
      console.error('Error analyzing text:', err);
      setError(err.message || 'Failed to analyze text. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportTasks = async () => {
    if (!generatedTasks.length) return;

    setIsImporting(true);
    let successCount = 0;

    try {
      // Import each task
      for (const task of generatedTasks) {
        try {
          const result = await addTask({
            title: task.title || 'Untitled Task',
            description: task.description || '',
            severity: task.severity || 'medium',
            propertyName: task.propertyName || 'General',
            propertyId: task.propertyId,
            dueDate: task.dueDate || 'No due date',
            providerId: task.providerId,
          });

          if (result) {
            successCount++;
          }
        } catch (taskError: any) {
          console.error('Error creating individual task:', taskError);
          // Continue with other tasks even if one fails
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully created ${successCount} maintenance tasks`);
        onTasksCreated();
        onOpenChange(false);
        // Reset form
        setInputText('');
        setGeneratedTasks([]);
      } else {
        toast.error('Failed to create any tasks');
      }
    } catch (err: any) {
      console.error('Error importing tasks:', err);
      toast.error('Error importing tasks: ' + (err.message || 'Unknown error'));
    } finally {
      setIsImporting(false);
    }
  };

  const handleClear = () => {
    setInputText('');
    setGeneratedTasks([]);
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate Maintenance Tasks with AI</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="maintenance-text">
              Paste text describing maintenance needs
            </Label>
            <Textarea
              id="maintenance-text"
              value={inputText}
              onChange={handleInputChange}
              onPaste={handlePaste}
              placeholder="Paste text here... e.g., 'The kitchen sink at Ocean View has a leak due by next Friday, and the hallway light bulb needs to be replaced as soon as possible.'"
              className="min-h-[150px]"
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleAnalyze}
              disabled={isLoading || !inputText.trim()}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : 'Generate Tasks'}
            </Button>
            <Button
              variant="outline"
              onClick={handleClear}
              disabled={isLoading || (!inputText && !generatedTasks.length)}
            >
              Clear
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {generatedTasks.length > 0 && (
            <div className="space-y-4 mt-4">
              <h3 className="font-medium">Generated Tasks ({generatedTasks.length})</h3>
              <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2">
                {generatedTasks.map((task, index) => (
                  <div key={index} className="border rounded-md p-4 space-y-2">
                    <p className="font-medium">{task.title}</p>
                    <p className="text-sm text-muted-foreground">{task.description}</p>
                    <div className="flex flex-wrap items-center gap-2">
                      {task.propertyName && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">
                          {task.propertyName}
                        </span>
                      )}
                      {task.providerId && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-800 text-xs font-medium">
                          Provider: {providers.find(p => p.id === task.providerId)?.name || 'Assigned'}
                        </span>
                      )}
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        task.severity === 'high' || task.severity === 'critical'
                          ? 'bg-red-100 text-red-800'
                          : task.severity === 'medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                      }`}>
                        {task.severity} priority
                      </span>
                      {task.dueDate && task.dueDate !== 'No due date' && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-800 text-xs font-medium">
                          Due: {task.dueDate}
                        </span>
                      )}
                      <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">
                        draft
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isImporting}>
            Cancel
          </Button>
          {generatedTasks.length > 0 && (
            <Button
              onClick={handleImportTasks}
              disabled={isImporting}
              className="gap-1"
            >
              {isImporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-4 w-4" />
                  Import {generatedTasks.length} Tasks
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AiMaintenanceDialog;
