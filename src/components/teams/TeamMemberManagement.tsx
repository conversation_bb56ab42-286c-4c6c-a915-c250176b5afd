import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/supabase';
import InviteTeamMemberDialog from './InviteTeamMemberDialog';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { UserPlus, Trash2, Loader2, Users } from 'lucide-react';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';


interface TeamMemberManagementProps {
  teamId: string;
}

const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({ teamId }) => {
  const { authState } = useAuth();
  const {
    teamMembers,
    teams,
    loading,
    fetchTeamMembers,
    inviteUserToTeam,
    removeTeamMember
  } = useTeamManagement();

  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const { hasPermission } = usePermissions();

  const [isProcessing, setIsProcessing] = useState(false);

  // Get the selected team from useTeamManagement
  const { selectedTeam } = useTeamManagement();

  // Check if the current user is the team owner - simplified
  const isTeamOwner = teams.find(t => t.id === teamId)?.owner_id === authState.user?.id;

  const isAdmin = authState?.profile?.role === 'admin' || authState?.profile?.is_super_admin;
  const isPropertyManager = authState?.profile?.role === 'property_manager';

  // Team owners and admins can manage staff and providers
  // Property managers should also be able to invite team members
  const canManageStaff = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_STAFF, teamId);
  const canManageProviders = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_SERVICE_PROVIDERS, teamId);

  // Force enable invite button for property managers
  const canInviteMembers = isTeamOwner || isAdmin || isPropertyManager || canManageStaff || canManageProviders;

  // For debugging
  console.log('TeamMemberManagement - Permissions:', {
    isTeamOwner,
    isAdmin,
    isPropertyManager,
    canManageStaff,
    canManageProviders,
    canInviteMembers,
    teamId,
    selectedTeamId: selectedTeam?.id,
    userId: authState.user?.id,
    teamOwnerId: selectedTeam?.owner_id,
    userEmail: authState.user?.email,
    userRole: authState?.profile?.role
  });

  // Track if component is mounted to prevent state updates after unmount
  const [isMounted, setIsMounted] = useState(true);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Get the query client outside of the function
  const queryClient = useQueryClient();

  // Function to load team members using React Query's built-in functionality
  const loadTeamMembers = async (force = false) => {
    if (!teamId || (loading && !force)) return;

    console.log('TeamMemberManagement - Fetching team members for team:', teamId, force ? '(forced)' : '');
    try {
      // First try a direct database query to ensure we have the latest data
      try {
        console.log('TeamMemberManagement - Trying direct database query first');
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            *,
            profiles:user_id (id, email, first_name, last_name, role)
          `)
          .eq('team_id', teamId);

        if (error) {
          console.error('TeamMemberManagement - Error in direct database query:', error);
        } else if (data && data.length > 0) {
          console.log(`TeamMemberManagement - Direct query found ${data.length} team members`);

          // Log the data for debugging
          console.log('Team members found via direct query:', data.length, data);

          // Format the data to match the expected format
          const formattedMembers = data.map(member => ({
            id: member.id,
            team_id: member.team_id,
            user_id: member.user_id,
            added_by: member.added_by,
            status: member.status,
            created_at: member.created_at,
            updated_at: member.updated_at,
            user_email: member.profiles?.email || '',
            user_name: `${member.profiles?.first_name || ''} ${member.profiles?.last_name || ''}`.trim() || 'Unknown User',
            role: member.profiles?.role || 'user'
          }));

          console.log('Formatted team members:', formattedMembers);
        }
      } catch (dbErr) {
        console.error('TeamMemberManagement - Error in direct database query:', dbErr);
      }

      // Then use the hook's fetch method
      await fetchTeamMembers(teamId);

      // Then invalidate the cache to ensure fresh data
      await queryClient.invalidateQueries({
        queryKey: ['teamMembersV2', teamId],
        exact: false
      });

      await queryClient.refetchQueries({
        queryKey: ['teamMembersV2', teamId],
        exact: false,
        type: 'all'
      });

      if (isMounted) {
        console.log('TeamMemberManagement - Team members fetched successfully through React Query');

        // Log the current team members from the hook
        console.log('TeamMemberManagement - Team members updated:', teamMembers.length);
      }
    } catch (err) {
      console.error('TeamMemberManagement - Error fetching team members:', err);
      if (isMounted) {
        toast({
          title: "Error",
          description: "Failed to load team members. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  // Single useEffect for loading team members to prevent multiple loads and flashing
  useEffect(() => {
    // Only load if we have a teamId
    if (!teamId) return;

    console.log('TeamMemberManagement - Team ID changed, loading members');

    // Track if the component is still mounted
    let isMounted = true;

    // Create a loading flag to prevent duplicate loads
    let isLoading = false;

    const loadData = async () => {
      // Prevent duplicate loads
      if (isLoading) return;
      isLoading = true;

      try {
        console.log(`TeamMemberManagement - Loading team members for team ${teamId}`);

        // First try a direct database query to ensure we have the latest data
        try {
          console.log('TeamMemberManagement - Trying direct database query');
          const { data, error } = await supabase
            .from('team_members')
            .select(`
              *,
              profiles:user_id (id, email, first_name, last_name, role)
            `)
            .eq('team_id', teamId);

          if (error) {
            console.error('TeamMemberManagement - Error in direct database query:', error);
          } else if (data && data.length > 0 && isMounted) {
            console.log(`TeamMemberManagement - Direct query found ${data.length} team members`);
          }
        } catch (dbErr) {
          console.error('TeamMemberManagement - Error in direct database query:', dbErr);
        }

        // Then use the hook's fetch method
        if (isMounted) {
          await fetchTeamMembers(teamId);

          // Then invalidate the cache to ensure fresh data
          await queryClient.invalidateQueries({
            queryKey: ['teamMembersV2', teamId],
            exact: false
          });

          console.log('TeamMemberManagement - Team members fetched successfully');
        }
      } catch (err) {
        console.error('TeamMemberManagement - Error fetching team members:', err);
        if (isMounted) {
          toast({
            title: "Error",
            description: "Failed to load team members. Please try again.",
            variant: "destructive"
          });
        }
      } finally {
        isLoading = false;
      }
    };

    // Load data immediately
    loadData();

    // Set up a fallback timer to ensure team members are loaded
    // but only if we don't already have team members
    const fallbackTimer = setTimeout(() => {
      if (teamMembers.length === 0 && isMounted) {
        console.log('TeamMemberManagement - Fallback timer: No team members loaded yet, retrying');
        loadData();
      }
    }, 2000);

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(fallbackTimer);
    };
  }, [teamId, queryClient, fetchTeamMembers]); // Remove teamMembers.length to prevent flashing

  // Removed custom event listener for tab changes
  // React Query will handle data refreshing automatically

  // Add a separate effect to log team members when they change
  useEffect(() => {
    if (teamMembers.length > 0) {
      console.log('TeamMemberManagement - Team members updated:', teamMembers.length);
    }
  }, [teamMembers]);

  const handleInviteUser = async (email: string, role: UserRole) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      await inviteUserToTeam(teamId, email, role);
      toast.success(`Invitation sent to ${email}`);
      setInviteDialogOpen(false);
    } catch (error) {
      console.error('Error inviting user:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const success = await removeTeamMember(memberId);
      if (success) {
        // Reload team members after removal
        await fetchTeamMembers(teamId);
        toast.success('Team member removed successfully');
      }
    } catch (error) {
      console.error('Error removing team member:', error);
      toast.error('Failed to remove team member');
    } finally {
      setIsProcessing(false);
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || '';
    const last = lastName?.charAt(0) || '';
    return (first + last).toUpperCase() || 'U';
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 sm:p-6 flex justify-between items-center">
          <h3 className="text-lg font-medium">Team Members</h3>
          {canInviteMembers && (
            <Button
              onClick={() => setInviteDialogOpen(true)}
              className="flex items-center gap-2"
              disabled={isProcessing}
            >
              <UserPlus className="h-4 w-4" />
              <span>Invite</span>
            </Button>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : teamMembers.length === 0 ? (
          <div className="text-center p-8 border-t">
            <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">No team members yet</p>
            {canInviteMembers && (
              <Button
                onClick={() => setInviteDialogOpen(true)}
                variant="outline"
                disabled={isProcessing}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Members
              </Button>
            )}
          </div>
        ) : (
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teamMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.avatar_url || undefined} />
                          <AvatarFallback>
                            {getInitials(member.first_name, member.last_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {member.first_name || member.last_name
                              ? `${member.first_name || ''} ${member.last_name || ''}`.trim()
                              : 'Unnamed User'}
                          </div>
                          <div className="text-xs text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {member.profile_role === 'property_manager'
                          ? 'Property Manager'
                          : member.profile_role === 'service_provider'
                            ? 'Service Provider'
                            : member.profile_role || 'User'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span className={`h-2 w-2 rounded-full mr-2 ${member.status === 'active' ? 'bg-green-500' : 'bg-amber-500'}`}></span>
                        <span className="capitalize">{member.status}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {canInviteMembers && member.user_id !== authState.user?.id && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveMember(member.id)}
                          className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                          disabled={isProcessing}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Remove</span>
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      <InviteTeamMemberDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        onInvite={handleInviteUser}
        canInviteStaff={isTeamOwner || isAdmin || isPropertyManager || canManageStaff}
        canInviteProviders={isTeamOwner || isAdmin || isPropertyManager || canManageProviders}
        isTeamOwner={isTeamOwner}
        isAdmin={isAdmin}
        isPropertyManager={isPropertyManager}
        isLoading={isProcessing}
      />
    </Card>
  );
};

export default TeamMemberManagement;
