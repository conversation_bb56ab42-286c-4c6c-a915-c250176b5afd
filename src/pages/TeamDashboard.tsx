import { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useQueryClient } from '@tanstack/react-query';
import TeamManagement from '@/components/teams/TeamManagement';
import TeamMemberManagement from '@/components/teams/TeamMemberManagement';
import PermissionManagement from '@/components/teams/PermissionManagement';
import TeamSettings from '@/components/teams/TeamSettings';
import SentInvitationsDisplay from '@/components/teams/SentInvitationsDisplay';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';
import { Users, Cog, Shield, Mail } from 'lucide-react';
import { StandardPageHeader, StandardLoadingState, StandardErrorState } from '@/components/ui/StandardizedUI';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { Skeleton } from '@/components/ui/skeleton';
import { PermissionType } from '@/types/auth';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';

import { useNavigate } from 'react-router-dom';
import { handleAuthError } from '@/utils/authErrorHandler';

const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // 2 seconds
const PENDING_INVITATIONS_FETCH_INTERVAL = 300000; // 5 minutes

const TeamDashboard = () => {
  const { authState, refreshProfile } = useAuth();
  const navigate = useNavigate();
  const { hasPermission, loading: permissionsLoading } = usePermissions();
  const queryClient = useQueryClient();
  const {
    loading,
    teams,
    selectedTeam,
    setSelectedTeam,
    fetchUserTeams,
    acceptTeamInvitation,
    fetchTeamMembers
  } = useTeamManagement();

  const [activeTab, setActiveTab] = useState('members');
  const isMobile = useIsMobile();
  const [pendingInvitations, setPendingInvitations] = useState<any[]>([]);
  const [loadingInvitations, setLoadingInvitations] = useState(false);
  const [networkError, setNetworkError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastInvitationFetch, setLastInvitationFetch] = useState(0);
  const [invitationsFetchRequestId, setInvitationsFetchRequestId] = useState(0);

  // Use refs to track initialization state
  const initialLoadDone = useRef(false);
  const teamsLoadedRef = useRef(false);
  const invitationsLoadedRef = useRef(false);

  const fetchPendingInvitations = useCallback(async (force = false) => {
    if (!authState.user?.id || (loadingInvitations && !force)) return;

    // Skip if invitations are already loaded and this isn't a forced refresh
    if (!force && invitationsLoadedRef.current) {
      const now = Date.now();
      if (now - lastInvitationFetch < PENDING_INVITATIONS_FETCH_INTERVAL) {
        return;
      }
    } else {
      const now = Date.now();
      if (!force && now - lastInvitationFetch < PENDING_INVITATIONS_FETCH_INTERVAL) {
        return;
      }
    }

    // Generate a unique request ID to track this specific fetch
    const requestId = invitationsFetchRequestId + 1;
    setInvitationsFetchRequestId(requestId);
    setLoadingInvitations(true);

    // Store current time for tracking when we last fetched
    const now = Date.now();

    try {
      const userEmail = authState?.profile?.email || authState.user.email;

      if (!userEmail) {
        console.error('Unable to get user email');
        return;
      }

      console.log('Fetching pending invitations for email:', userEmail);

      // Fetch pending invitations with team information
      const { data, error } = await supabase
        .from('team_invitations')
        .select('*, teams:team_id(name)')
        .eq('email', userEmail)
        .eq('status', 'pending');

      // Only update state if this is still the most recent request
      if (requestId === invitationsFetchRequestId) {
        if (error) {
          console.error('Error fetching pending invitations:', error);
        } else {
          console.log('Pending invitations:', data);
          setPendingInvitations(data || []);
          setLastInvitationFetch(now);
          invitationsLoadedRef.current = true;
        }
        setLoadingInvitations(false);
      } else {
        console.log('Ignoring outdated invitation fetch response');
      }
    } catch (error) {
      // Only update state if this is still the most recent request
      if (requestId === invitationsFetchRequestId) {
        console.error('Error fetching pending invitations:', error);
        setLoadingInvitations(false);
      }
    }
  }, [authState.user?.id, authState?.profile?.email, authState.user?.email, loadingInvitations, lastInvitationFetch, invitationsFetchRequestId]);

  const fetchTeams = useCallback(async (retryAttempt = 0) => {
    if (!authState.user?.id) {
      console.log('[TeamDashboard] No user ID, skipping team fetch');
      return;
    }

    if (loading && retryAttempt === 0) {
      console.log('[TeamDashboard] Already loading, skipping team fetch');
      return;
    }

    try {
      console.log('[TeamDashboard] Fetching teams for user:', authState.user.id, 'Role:', authState?.profile?.role);
      setNetworkError(false);

      // Use regular database approach for all environments
      await fetchUserTeams();
      console.log('[TeamDashboard] Teams fetched successfully:', teams);
      teamsLoadedRef.current = true;
    } catch (error: any) {
      console.error('[TeamDashboard] Error fetching teams:', error);

      // Check if this is an authentication error
      if (error.message?.includes('Auth') || error.message?.includes('auth') || error.status === 401) {
        handleAuthError(error, {
          showToast: true,
          redirectToLogin: true,
          logError: true
        });
        return;
      }

      // Handle network errors with retry logic
      if ((error.message?.includes('Failed to fetch') || error.message?.includes('ERR_INSUFFICIENT_RESOURCES')) && retryAttempt < MAX_RETRY_ATTEMPTS) {
        setRetryCount(prev => prev + 1);
        console.log(`[TeamDashboard] Retrying team fetch (attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS})`);
        setTimeout(() => {
          fetchTeams(retryAttempt + 1);
        }, RETRY_DELAY * (retryAttempt + 1));
      } else {
        setNetworkError(true);
        toast.error('Failed to load teams. Network issue detected.');
      }
    }
  }, [authState.user?.id, authState?.profile?.role, fetchUserTeams, loading, teams, selectedTeam]);

  // Initial data load - use a ref to prevent multiple loads
  const initialDataLoadingRef = useRef(false);

  useEffect(() => {
    if (!authState.user?.id || initialLoadDone.current || initialDataLoadingRef.current) return;

    const loadInitialData = async () => {
      if (initialDataLoadingRef.current) return;
      initialDataLoadingRef.current = true;

      try {
        console.log('Initial data load for TeamDashboard');
        initialLoadDone.current = true;

        // First refresh the auth profile
        if (refreshProfile) {
          try {
            await refreshProfile();
            console.log('Auth profile refreshed successfully');
          } catch (err) {
            console.error('Error refreshing auth profile:', err);
            // Continue anyway
          }
        }

        // Then load teams and invitations
        await fetchTeams(0);
        await fetchPendingInvitations(true);

        // If we have a selected team and we're on the members tab, directly fetch team members
        if (selectedTeam?.id && activeTab === 'members') {
          console.log(`[TeamDashboard] Initial data load - directly fetching team members for team: ${selectedTeam.id}`);
          try {
            await fetchTeamMembers(selectedTeam.id);
            console.log('[TeamDashboard] Initial team members fetch successful');
          } catch (err) {
            console.error('[TeamDashboard] Error in initial team members fetch:', err);
          }
        }

        // Use React Query's built-in functionality instead of custom events
        console.log(`[TeamDashboard] Initial load complete, using React Query's built-in functionality for data loading`);

        // No need to manually trigger refreshes - React Query will handle this automatically
      } catch (error) {
        console.error('Error in initial data load:', error);
      } finally {
        initialDataLoadingRef.current = false;
      }
    };

    // Use setTimeout to ensure this runs after other effects
    setTimeout(loadInitialData, 100);
  }, [authState.user?.id]); // Simplified dependencies to prevent re-runs

  // Periodic invitation check - use a ref to prevent multiple setups
  const invitationIntervalSetupRef = useRef(false);

  useEffect(() => {
    if (!authState.user?.id || invitationIntervalSetupRef.current) return;

    // Mark as set up to prevent multiple intervals
    invitationIntervalSetupRef.current = true;

    // Only check if we haven't loaded invitations yet or it's been a while
    if (!invitationsLoadedRef.current || Date.now() - lastInvitationFetch > PENDING_INVITATIONS_FETCH_INTERVAL) {
      // Add a small delay to prevent immediate fetch
      setTimeout(() => {
        fetchPendingInvitations(false);
      }, 2000);
    }

    // Set up interval for checking invitations - much less frequently
    const intervalId = setInterval(() => {
      fetchPendingInvitations(false);
    }, PENDING_INVITATIONS_FETCH_INTERVAL);

    return () => {
      clearInterval(intervalId);
      invitationIntervalSetupRef.current = false;
    };
  }, [authState.user?.id, fetchPendingInvitations, lastInvitationFetch]);

  // Use React Query's built-in functionality for data refreshing
  // No need for custom event listeners or visibility change tracking

  // Use the useNavigationRefresh hook to refresh data when navigating to this page
  const { refreshRouteData } = useNavigationRefresh();

  // Set up a one-time effect to refresh data when the component mounts
  // Use a ref to ensure this only runs once
  const componentMountedRef = useRef(false);

  useEffect(() => {
    // Skip if no user ID or if this effect has already run
    if (!authState.user?.id || componentMountedRef.current) return;

    // Mark that this effect has run
    componentMountedRef.current = true;

    console.log('[TeamDashboard] Component mounted, refreshing route data');

    // First, directly fetch teams to ensure we have data
    console.log('[TeamDashboard] Directly fetching teams on mount');
    fetchTeams(0).then(async () => {
      console.log('[TeamDashboard] Direct teams fetch completed');

      // After teams are fetched, refresh route data to ensure all related data is loaded
      // Teams route is no longer blacklisted, so this will work properly
      console.log('[TeamDashboard] Calling refreshRouteData for /teams');
      await refreshRouteData('/teams');
      console.log('[TeamDashboard] Finished refreshRouteData for /teams');

      // Also dispatch a custom event to notify that we're on the teams page
      window.dispatchEvent(new CustomEvent('stayfu-page-loaded', {
        detail: {
          page: 'teams',
          path: '/teams'
        }
      }));

      // Force a direct query to check team ownership, but only if we have a selected team
      if (selectedTeam?.id && authState.user?.id) {
        console.log(`[TeamDashboard] Checking team ownership for team ${selectedTeam.id} and user ${authState.user.id}`);
        supabase
          .from('teams')
          .select('owner_id')
          .eq('id', selectedTeam.id)
          .single()
          .then(({ data, error }) => {
            if (error) {
              console.error('[TeamDashboard] Error checking team ownership:', error);
            } else if (data) {
              const isOwner = data.owner_id === authState.user?.id;
              console.log(`[TeamDashboard] Team ownership check: User is ${isOwner ? '' : 'NOT '}the owner of team ${selectedTeam.id}`);

              // If the user is the owner, force a refresh of the permissions
              if (isOwner) {
                console.log('[TeamDashboard] User is team owner, refreshing permissions');
                queryClient.invalidateQueries({ queryKey: ['teamPermissionsV2'] });
                queryClient.refetchQueries({ queryKey: ['teamPermissionsV2'] });
              }
            }
          });
      }
    }).catch(err => {
      console.error('[TeamDashboard] Error in direct teams fetch:', err);

      // Still try to refresh route data even if direct fetch fails
      refreshRouteData('/teams');
    });
  }, [authState.user?.id]); // Only depend on user ID to prevent re-runs

  // Set up a periodic refresh timer instead of using visibility change events
  useEffect(() => {
    // Skip if no user ID
    if (!authState.user?.id) return;

    console.log('[TeamDashboard] Setting up periodic refresh timer');

    // Function to refresh all team data
    const refreshAllTeamData = async () => {
      try {
        console.log('[TeamDashboard] Periodic refresh triggered');

        // First invalidate all relevant queries
        console.log('[TeamDashboard] Invalidating all team-related queries');
        queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
        queryClient.invalidateQueries({ queryKey: ['teamMembersV2'] });
        queryClient.invalidateQueries({ queryKey: ['teamInvitationsV2'] });
        queryClient.invalidateQueries({ queryKey: ['teamPermissionsV2'] });
        queryClient.invalidateQueries({ queryKey: ['teamPropertiesV2'] });

        // Also invalidate legacy query keys for backward compatibility
        queryClient.invalidateQueries({ queryKey: ['teams'] });
        queryClient.invalidateQueries({ queryKey: ['teamMembers'] });
        queryClient.invalidateQueries({ queryKey: ['teamInvitations'] });
        queryClient.invalidateQueries({ queryKey: ['teamPermissions'] });
        queryClient.invalidateQueries({ queryKey: ['teamProperties'] });

        // Then refetch all the queries we invalidated
        await Promise.all([
          queryClient.refetchQueries({ queryKey: ['teamsV2'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamMembersV2'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamInvitationsV2'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamPermissionsV2'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamPropertiesV2'], type: 'all' }),
          // Also refetch legacy query keys
          queryClient.refetchQueries({ queryKey: ['teams'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamMembers'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamInvitations'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamPermissions'], type: 'all' }),
          queryClient.refetchQueries({ queryKey: ['teamProperties'], type: 'all' })
        ]);

        // Also directly fetch teams to ensure we have the latest data
        console.log('[TeamDashboard] Directly fetching teams data');
        await fetchTeams(0);

        // If we have pending invitations, refresh those too
        if (pendingInvitations.length > 0 || !invitationsLoadedRef.current) {
          console.log('[TeamDashboard] Refreshing pending invitations');
          await fetchPendingInvitations(true);
        }

        console.log('[TeamDashboard] Periodic data refresh complete');
      } catch (error) {
        console.error('[TeamDashboard] Error in periodic refresh:', error);
      }
    };

    // Initial refresh after a short delay
    const initialRefreshTimeout = setTimeout(() => {
      refreshAllTeamData();
    }, 2000);

    // Set up periodic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      // Only refresh if the document is visible
      if (document.visibilityState === 'visible') {
        refreshAllTeamData();
      } else {
        console.log('[TeamDashboard] Document not visible, skipping periodic refresh');
      }
    }, 300000); // 5 minutes (300000 ms)

    // Clean up
    return () => {
      clearTimeout(initialRefreshTimeout);
      clearInterval(refreshInterval);
    };
  }, [authState.user?.id]); // Simplified dependencies to prevent excessive re-renders

  const handleRefresh = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      if (authState.user?.id) {
        console.log('[TeamDashboard] Manual refresh initiated');

        // First fetch the data directly to ensure we have the latest
        console.log('[TeamDashboard] Fetching teams and invitations directly');
        await Promise.all([
          fetchTeams(0),
          fetchPendingInvitations(true)
        ]);

        // If we have a selected team and we're on the members tab, directly fetch team members
        if (selectedTeam?.id && activeTab === 'members') {
          console.log(`[TeamDashboard] Refresh - directly fetching team members for team: ${selectedTeam.id}`);
          try {
            await fetchTeamMembers(selectedTeam.id);
            console.log('[TeamDashboard] Refresh team members fetch successful');
          } catch (err) {
            console.error('[TeamDashboard] Error in refresh team members fetch:', err);
          }
        }

        // Then use our enhanced navigation refresh to refresh all team-related queries
        console.log('[TeamDashboard] Using enhanced navigation refresh for /teams');
        await refreshRouteData('/teams');

        console.log(`[TeamDashboard] Manual refresh complete`);
        toast.success('Dashboard refreshed');
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('Failed to refresh dashboard');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleAcceptInvitation = async (token: string) => {
    try {
      console.log('TeamDashboard: Accepting invitation with token:', token);
      const result = await acceptTeamInvitation(token);

      if (result.success) {
        console.log('TeamDashboard: Successfully accepted invitation, refreshing data');
        toast.success('You have successfully joined the team!');

        // Fetch teams again to update the list
        await fetchTeams();

        // Refresh pending invitations
        await fetchPendingInvitations(true);

        // Force a complete refresh of the dashboard
        setTimeout(() => {
          handleRefresh();
        }, 1000);
      } else {
        console.error('TeamDashboard: Failed to accept invitation:', result.error);
        toast.error(result.error || 'Failed to accept invitation');
      }
    } catch (error: any) {
      console.error('TeamDashboard: Error accepting invitation:', error);
      toast.error('Error accepting invitation: ' + error.message);
    }
  };

  if ((loading || permissionsLoading) && !networkError && !teamsLoadedRef.current) {
    return (
      <div className="container mx-auto p-4 sm:p-6 space-y-6">
        <StandardLoadingState message="Loading team data..." />
      </div>
    );
  }

  if (networkError) {
    return (
      <div className="container mx-auto p-4 sm:p-6 space-y-6">
        <StandardErrorState
          message={`Network connection issue detected. Unable to fetch team data. ${retryCount > 0 ? `(Attempted ${retryCount} retries)` : ''}`}
          retryLabel="Retry Connection"
          onRetry={() => {
            setRetryCount(0);
            fetchTeams(0);
            fetchPendingInvitations(true);
          }}
        />
      </div>
    );
  }

  // Check if the user is the team owner
  const isTeamOwner = selectedTeam ? selectedTeam.owner_id === authState.user?.id : false;
  const isAdmin = authState?.profile?.role === 'admin' || authState?.profile?.is_super_admin;
  const isPropertyManager = authState?.profile?.role === 'property_manager';

  // Team owners and admins can manage staff and providers
  const canManageStaff = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_STAFF, selectedTeam?.id);
  const canManageProviders = isTeamOwner || isAdmin || isPropertyManager || hasPermission(PermissionType.MANAGE_SERVICE_PROVIDERS, selectedTeam?.id);

  // Force enable permissions for property managers
  const canManageTeam = isTeamOwner || isAdmin || isPropertyManager || canManageStaff || canManageProviders;

  // Always enable permissions for the team owner
  const canManagePermissions = isTeamOwner || isAdmin || hasPermission(PermissionType.MANAGE_STAFF, selectedTeam?.id);

  // For debugging
  console.log('Team Dashboard - Permissions:', {
    isTeamOwner,
    isAdmin,
    isPropertyManager,
    canManageStaff,
    canManageProviders,
    canManageTeam,
    canManagePermissions,
    selectedTeam: selectedTeam?.id,
    userId: authState.user?.id,
    teamOwnerId: selectedTeam?.owner_id,
    userEmail: authState.user?.email,
    userRole: authState?.profile?.role
  });

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      <StandardPageHeader
        title="Team Dashboard"
        description="Manage your team members and their permissions"
        onRefresh={handleRefresh}
        isLoading={isRefreshing}
        secondaryActions={
          <TeamManagement onTeamCreated={() => {
            fetchTeams();
          }} />
        }
      />

      {loadingInvitations && !invitationsLoadedRef.current ? (
        <Card className="p-4 sm:p-6">
          <div className="flex items-center gap-2 mb-4">
            <Mail className="h-5 w-5 text-muted-foreground" />
            <h2 className="font-semibold">Checking for invitations...</h2>
          </div>
          <div className="space-y-4">
            <Skeleton className="h-16 w-full" />
          </div>
        </Card>
      ) : pendingInvitations.length > 0 ? (
        <Card className="p-4 sm:p-6 bg-amber-50 border-amber-200">
          <div className="flex items-center gap-2 mb-4">
            <Mail className="h-5 w-5 text-amber-600" />
            <h2 className="font-semibold text-amber-800">Pending Invitations</h2>
          </div>
          <div className="space-y-4">
            {pendingInvitations.map((invitation) => (
              <div key={invitation.id} className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 p-3 bg-white rounded-md border border-amber-100">
                <div>
                  <p className="font-medium">
                    {invitation.teams?.name ||
                     (invitation.team_id ? `Team ID: ${invitation.team_id.substring(0, 8)}...` : 'Unknown Team')}
                  </p>
                  <p className="text-sm text-muted-foreground">Role: {invitation.role === 'service_provider' ? 'Service Provider' : invitation.role}</p>
                </div>
                <div className="flex gap-2 w-full sm:w-auto mt-2 sm:mt-0">
                  <Button
                    size="sm"
                    className="flex-1 sm:flex-none"
                    onClick={() => handleAcceptInvitation(invitation.token)}
                  >
                    Accept
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 sm:flex-none"
                    onClick={() => navigate(`/invite?token=${invitation.token}&team_id=${invitation.team_id}`)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      ) : null}

      {teams.length === 0 ? (
        <Card className="p-4 sm:p-6 flex flex-col items-center justify-center gap-4">
          <p className="text-muted-foreground text-center">You don't have any teams yet.</p>
          <TeamManagement onTeamCreated={() => {
            fetchTeams();
          }} />
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-12 gap-4 sm:gap-6">
          <Card className={`p-4 ${isMobile ? 'col-span-1' : 'col-span-1 md:col-span-3'}`}>
            <h2 className="font-medium mb-4">Your Teams</h2>
            <div className={`${isMobile ? 'grid grid-cols-2 gap-2' : 'space-y-2'}`}>
              {teams.map(team => (
                <Button
                  key={team.id}
                  variant={selectedTeam?.id === team.id ? "default" : "outline"}
                  className={`${isMobile ? 'w-full text-xs px-2 py-1' : 'w-full justify-between'}`}
                  onClick={() => {
                    // Only take action if this is a different team
                    if (selectedTeam?.id !== team.id) {
                      console.log(`[TeamDashboard] Changing selected team from ${selectedTeam?.id || 'none'} to ${team.id}`);

                      // Update the selected team
                      setSelectedTeam(team);

                      // Let React Query handle data refreshing - no need for direct fetches
                      console.log(`[TeamDashboard] Team changed to: ${team.name}, React Query will handle data refreshing`);

                      // Dispatch a custom event to notify that the team has changed
                      window.dispatchEvent(new CustomEvent('stayfu-team-changed', {
                        detail: {
                          teamId: team.id,
                          teamName: team.name
                        }
                      }));
                    } else {
                      console.log(`[TeamDashboard] Team ${team.id} already selected, no action needed`);
                    }
                  }}
                >
                  <span className="truncate">{team.name}</span>
                  {!isMobile && (
                    <span className="bg-primary/10 text-xs rounded-full px-2 py-1 ml-2">
                      {team.member_count || 0}
                    </span>
                  )}
                </Button>
              ))}
            </div>
          </Card>

          <div className={`${isMobile ? 'col-span-1' : 'col-span-1 md:col-span-9'}`}>
            {selectedTeam && (
              <Card className="p-4 sm:p-6">
                <div className="flex justify-between items-center mb-4 sm:mb-6">
                  <h2 className="text-lg sm:text-xl font-bold truncate pr-2">{selectedTeam.name}</h2>
                </div>

                <Tabs
                  value={activeTab}
                  onValueChange={(value) => {
                    // Only take action if this is a different tab
                    if (activeTab !== value) {
                      console.log(`[TeamDashboard] Changing tab from ${activeTab} to ${value}`);

                      // Update the active tab
                      setActiveTab(value);

                      // Let React Query handle data refreshing - no need for direct fetches
                      console.log(`[TeamDashboard] Tab changed to: ${value}, React Query will handle data refreshing`);

                      // Dispatch a custom event to notify that the tab has changed
                      window.dispatchEvent(new CustomEvent('stayfu-tab-changed', {
                        detail: {
                          tab: value,
                          teamId: selectedTeam?.id
                        }
                      }));

                      // Refresh route data for the current route
                      refreshRouteData(window.location.hash || '/teams');
                    } else {
                      console.log(`[TeamDashboard] Tab ${value} already selected, no action needed`);
                    }
                  }}
                >
                  <TabsList className="mb-4 w-full grid grid-cols-3 overflow-x-auto">
                    <TabsTrigger value="members" className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span className="hidden xs:inline">Members</span>
                    </TabsTrigger>
                    <TabsTrigger value="permissions" className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      <span className="hidden xs:inline">Permissions</span>
                    </TabsTrigger>
                    <TabsTrigger value="settings" className="flex items-center gap-2">
                      <Cog className="h-4 w-4" />
                      <span className="hidden xs:inline">Settings</span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="members" className="space-y-4">
                    <TeamMemberManagement teamId={selectedTeam.id} />
                    {/* Display sent invitations below team members */}
                    <SentInvitationsDisplay teamId={selectedTeam.id} />
                  </TabsContent>

                  <TabsContent value="permissions" className="space-y-0">
                    <PermissionManagement teamId={selectedTeam.id} />
                  </TabsContent>

                  <TabsContent value="settings" className="space-y-0">
                    <TeamSettings
                      team={selectedTeam}
                      onTeamUpdated={() => {
                        fetchTeams();
                      }}
                    />
                  </TabsContent>
                </Tabs>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TeamDashboard;
